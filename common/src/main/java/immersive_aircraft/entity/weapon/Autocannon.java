package immersive_aircraft.entity.weapon;

import immersive_aircraft.Sounds;
import immersive_aircraft.cobalt.network.NetworkHandler;
import immersive_aircraft.config.Config;
import immersive_aircraft.entity.VehicleEntity;
import immersive_aircraft.entity.misc.WeaponMount;
import immersive_aircraft.network.c2s.FireMessage;
import immersive_aircraft.resources.bbmodel.BBAnimationVariables;
import net.minecraft.core.registries.BuiltInRegistries;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraft.world.entity.projectile.Arrow;
import net.minecraft.world.item.ItemStack;
import net.minecraft.network.chat.Component;
import net.minecraft.world.phys.Vec3;
import org.joml.Vector3f;
import org.joml.Vector4f;

import java.util.Map;
import java.util.Objects;

public class Autocannon extends BulletWeapon {
    private static final float MAX_COOLDOWN = 0.15f; // Fast firing rate like an autocannon
    private float cooldown = 0.0f;
    private int ammo = 0;
    private ItemStack ammoStack;

    // Hardcoded autocannon ammunition for Create Big Cannons and Create Advanced Technologies
    private static final Map<String, String> AUTOCANNON_AMMO_MAP;
    
    static {
        Map<String, String> map = new java.util.HashMap<>();
        
        // Create Big Cannons autocannon ammo
        map.put("createbigcannons:autocannon_cartridge", "so_lol_we_dont_know");
        map.put("createbigcannons:machine_gun_round", "createbigcannons:machine_gun_projectile");
        
        // Create: Advanced Technologies autocannon ammo
        map.put("cbc_at:autocannon_cartridge", "createbigcannons:autocannon_projectile");
        map.put("cbc_at:machine_gun_round", "createbigcannons:machine_gun_projectile");
        map.put("cbc_at:autocannon_round", "createbigcannons:autocannon_projectile");
        
        // Fallback items

        
        AUTOCANNON_AMMO_MAP = java.util.Collections.unmodifiableMap(map);
    }


    public Autocannon(VehicleEntity entity, ItemStack stack, WeaponMount mount, int slot) {
        super(entity, stack, mount, slot);
    }

    @Override
    protected float getBarrelLength() {
        return 1.5f;
    }

    @Override
    protected Vector4f getBarrelOffset() {
        return new Vector4f(0.0f, 0.5f, 0.0f, 1.0f);
    }

    public float getVelocity() {
        return 6.0f; // High velocity for autocannon rounds
    }

    public float getInaccuracy() {
        return 0.5f; // More accurate than rotary cannon
    }

    @Override
    protected Entity getBullet(Entity shooter, Vector4f position, Vector3f direction) {
        if (ammoStack != null && !ammoStack.isEmpty()) {
            Entity autocannonRound = createAutocannonProjectile(shooter, position, direction);
            if (autocannonRound != null) {
                return autocannonRound;
            }
        }
        
        // Fallback to arrow
        Arrow arrow = new Arrow(shooter.level(), position.x(), position.y(), position.z());
        arrow.pickup = AbstractArrow.Pickup.DISALLOWED;
        arrow.setOwner(getEntity().getControllingPassenger());
        arrow.shoot(direction.x(), direction.y(), direction.z(), getVelocity(), getInaccuracy());
        return arrow;
    }

    private Entity createAutocannonProjectile(Entity shooter, Vector4f position, Vector3f direction) {
        String itemId = BuiltInRegistries.ITEM.getKey(ammoStack.getItem()).toString();
        System.out.println(itemId);
        // Try hardcoded mapping first
        if (AUTOCANNON_AMMO_MAP.containsKey(itemId)) {
            String entityId = AUTOCANNON_AMMO_MAP.get(itemId);
            System.out.println(entityId);
            if (Objects.equals(entityId, " so_lol_we_dont_know")) {
                String projectileId = ((CompoundTag)ammoStack.getTag().get("Projectile")).get("id").getAsString();
                String[] projectileParts = projectileId.split(":");
                String modId = projectileParts[0];
                String modEntityId = projectileParts[1];
                if (modId.equals("createbigcannons")) {
                    entityId = "createbigcannons:" + modEntityId.replace("_round", "");
                } else if (modId.equals("cbc_at")) {
                    entityId = modId + ":" + modEntityId.replace("item", "projectile");
                }
                System.out.println(entityId);
                Entity projectile = createProjectileEntity(entityId, shooter, position, direction);
                if (projectile != null) {
                    return projectile;
                }
            }
            Entity projectile = createProjectileEntity(entityId, shooter, position, direction);
            if (projectile != null) {
                return projectile;
            }
        }
        
        // Try auto-detection for Create Big Cannons ammo
        Entity projectile = createAutocannonFromBigCannonAmmo(ammoStack, shooter, position, direction);
        if (projectile != null) {
            return projectile;
        }
        
        return null;
    }

    private Entity createProjectileEntity(String entityId, Entity shooter, Vector4f position, Vector3f direction) {
        try {
            ResourceLocation entityLocation = ResourceLocation.tryParse(entityId);
            if (entityLocation != null && BuiltInRegistries.ENTITY_TYPE.containsKey(entityLocation)) {
                EntityType<?> entityType = BuiltInRegistries.ENTITY_TYPE.get(entityLocation);
                Entity projectile = entityType.create(shooter.level());
                if (projectile != null) {
                    setupProjectileProperties(projectile, ammoStack);
                    projectile.setPos(position.x(), position.y(), position.z());
                    
                    // Set velocity with aircraft inheritance
                    Vec3 aircraftVelocity = getEntity().getSpeedVector();
                    Vector3f totalVelocity = direction.mul(getVelocity(), new Vector3f());
                    projectile.setDeltaMovement(
                        totalVelocity.x() + aircraftVelocity.x(),
                        totalVelocity.y() + aircraftVelocity.y(),
                        totalVelocity.z() + aircraftVelocity.z()
                    );
                    
                    // Set owner if it's a projectile
                    if (projectile instanceof AbstractArrow arrow && getEntity().getControllingPassenger() != null) {
                        arrow.setOwner(getEntity().getControllingPassenger());
                    }
                    
                    // Apply ammo properties

                    
                    return projectile;
                }
            }
        } catch (Exception e) {
            // Entity creation failed
        }
        
        return null;
    }

    private Entity createAutocannonFromBigCannonAmmo(ItemStack ammoStack, Entity shooter, Vector4f position, Vector3f direction) {
        try {
            // Check if the item is an autocannon round from Create Big Cannons
            String itemId = BuiltInRegistries.ITEM.getKey(ammoStack.getItem()).toString();
            
            // Try different autocannon projectile naming patterns
            String[] projectilePatterns = {
                itemId.replace("_cartridge", "_projectile"),
                itemId.replace("_round", "_projectile"),
                itemId + "_projectile",
                "createbigcannons:autocannon_projectile" // Default CBC autocannon projectile
            };
            
            for (String pattern : projectilePatterns) {
                Entity projectile = createProjectileEntity(pattern, shooter, position, direction);
                if (projectile != null) {
                    return projectile;
                }
            }
            
        } catch (Exception e) {
            // Auto-detection failed
        }
        
        return null;
    }

    private void setupProjectileProperties(Entity projectile, ItemStack ammoStack) {
        try {
            CompoundTag projectileNBT = new CompoundTag();
            CompoundTag itemNBT = ammoStack.getTag();
            
            // Copy relevant NBT data from ammo to projectile
            if (itemNBT != null) {
                if (itemNBT.contains("Damage")) {
                    projectileNBT.putFloat("Damage", itemNBT.getFloat("Damage"));
                }
                if (itemNBT.contains("PierceLevel")) {
                    projectileNBT.putByte("PierceLevel", itemNBT.getByte("PierceLevel"));
                }
            }
            
            // Apply NBT to projectile
            if (!projectileNBT.isEmpty()) {
                projectile.load(projectileNBT);
            }
            
        } catch (Exception e) {
            // Property setup failed, projectile will use defaults
        }
    }

    @Override
    public void tick() {
        cooldown -= 1.0f / 20.0f;
    }

    @Override
    public void fire(Vector3f direction) {
        if (spentAmmo(1)) {
            super.fire(direction);
        }
    }

    @Override
    public void clientFire(int index) {
        if (cooldown <= 0.0f) {
            cooldown = MAX_COOLDOWN;
            NetworkHandler.sendToServer(new FireMessage(getSlot(), index, getDirection()));
        }
    }

    @Override
    public SoundEvent getSound() {
        return Sounds.CANNON.get(); // Use cannon sound for now
    }

    private Vector3f getDirection() {
        Vector3f direction = new Vector3f(0, 0, 1.0f);
        direction.mul(getEntity().getVehicleNormalTransform());
        return direction;
    }

    protected boolean spentAmmo(int amount) {
        if (ammo < amount && getEntity() instanceof immersive_aircraft.entity.InventoryVehicleEntity vehicle) {
            for (int i = 0; i < vehicle.getInventory().getContainerSize(); i++) {
                ItemStack stack = vehicle.getInventory().getItem(i);
                if (isAutocannonAmmo(stack)) {
                    ammoStack = stack.copy();
                    ammoStack.setCount(1);

                    if (!getEntity().isPilotCreative()) {
                        ammo += 1;
                        stack.shrink(1);
                    }
                    break;
                }
            }
        }

        if (getEntity().isPilotCreative()) {
            if (ammoStack == null || ammoStack.isEmpty()) {
                ammoStack = createDefaultAmmo();
            }
            return ammoStack != null && !ammoStack.isEmpty();
        }

        if (ammo <= 0) {
            if (getEntity().getControllingPassenger() instanceof Player player) {
                player.displayClientMessage(Component.translatable("immersive_aircraft.out_of_ammo"), true);
            }
            return false;
        }

        ammo -= amount;
        return true;
    }

    private boolean isAutocannonAmmo(ItemStack stack) {
        if (stack.isEmpty()) {
            return false;
        }

        String itemId = BuiltInRegistries.ITEM.getKey(stack.getItem()).toString();
        
        // Check hardcoded ammo map
        if (AUTOCANNON_AMMO_MAP.containsKey(itemId)) {
            return true;
        }


        // Check for autocannon-related naming patterns
        return itemId.contains("autocannon") || itemId.contains("machine_gun");
    }

    private ItemStack createDefaultAmmo() {
        // Try to create Create Big Cannons autocannon cartridge if available
        try {
            ResourceLocation ammoId = ResourceLocation.tryParse("createbigcannons:autocannon_cartridge");
            if (ammoId != null && BuiltInRegistries.ITEM.containsKey(ammoId)) {
                return new ItemStack(BuiltInRegistries.ITEM.get(ammoId));
            }
        } catch (Exception e) {
            // Fall through to fallback
        }

        // Fallback to arrow
        return new ItemStack(BuiltInRegistries.ITEM.get(ResourceLocation.tryParse("minecraft:arrow")));
    }
}
