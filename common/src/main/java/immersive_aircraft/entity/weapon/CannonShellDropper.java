package immersive_aircraft.entity.weapon;

import immersive_aircraft.entity.VehicleEntity;
import immersive_aircraft.entity.InventoryVehicleEntity;
import immersive_aircraft.entity.misc.WeaponMount;
import immersive_aircraft.cobalt.network.NetworkHandler;
import immersive_aircraft.network.c2s.FireMessage;
import net.minecraft.core.registries.BuiltInRegistries;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;

import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraft.world.entity.projectile.Arrow;
import net.minecraft.world.item.ItemStack;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.phys.Vec3;
import org.joml.Matrix3f;
import org.joml.Vector3f;
import org.joml.Vector4f;
import rbasamoyai.createbigcannons.cannons.big_cannons.BigCannonBlockItem;
import rbasamoyai.createbigcannons.munitions.FuzedProjectileBlockItem;
import rbasamoyai.createbigcannons.munitions.big_cannon.ProjectileBlock;

import java.util.Map;
import java.util.Set;
import java.util.Optional;

public class CannonShellDropper extends Weapon {
    private static final float MAX_COOLDOWN = 2.0f;
    private float cooldown = 0.0f;
    private int ammo = 0;
    private ItemStack ammoStack;

    // Fallback items if Create Big Cannons isn't available
    private static final Set<String> FALLBACK_AMMUNITION = Set.of(
            "minecraft:tnt",
            "minecraft:fire_charge"
    );

    public CannonShellDropper(VehicleEntity entity, ItemStack stack, WeaponMount mount, int slot) {
        super(entity, stack, mount, slot);
    }

    @Override
    public void tick() {
        cooldown -= 1.0f / 20.0f;
    }

    @Override
    public void fire(Vector3f direction) {
        if (spentAmmo(1)) {
            dropCannonShell(direction);
        }
    }

    @Override
    public void clientFire(int index) {
        if (cooldown <= 0.0f) {
            cooldown = MAX_COOLDOWN;
            NetworkHandler.sendToServer(new FireMessage(getSlot(), index, getDirection()));
        }

    }
    private void dropCannonShell(Vector3f direction) {
        // Calculate the drop position
        Vector4f position = getDropOffset();
        VehicleEntity entity = getEntity();
        position.mul(getMount().transform());
        position.mul(entity.getVehicleTransform());

        // Try to spawn Create Big Cannon shell entity
        if (ammoStack != null && !ammoStack.isEmpty()) {
            Entity shellEntity = createCannonShellEntity(entity, ammoStack, position, direction);

            if (shellEntity != null) {
                entity.level().addFreshEntity(shellEntity);
            }
        }

        // Play sound
        getEntity().playSound(SoundEvents.DISPENSER_DISPENSE, 1.0f, 0.8f + 0.4f * entity.level().random.nextFloat());
    }





    private Entity createCannonShellEntity(VehicleEntity vehicle, ItemStack ammoStack, Vector4f position, Vector3f direction) {
        String itemId = BuiltInRegistries.ITEM.getKey(ammoStack.getItem()).toString();
        System.out.println("[CannonShellDropper] Creating shell entity for: " + itemId);

        // Try to create shell entity based on auto-detection
        Entity shellEntity = null;

        // First, try to create the corresponding projectile entity
        shellEntity = createShellEntityFromItem(itemId, vehicle, position, direction);

        if (shellEntity == null) {
            System.out.println("[CannonShellDropper] Munition system failed, falling back to arrow for: " + itemId);
            // Fallback: create a simple arrow projectile for vanilla items
            Arrow arrow = new Arrow(vehicle.level(), position.x(), position.y(), position.z());
            arrow.setOwner(vehicle.getControllingPassenger());
            // Inherit aircraft velocity exactly
            arrow.setDeltaMovement(vehicle.getDeltaMovement());
            shellEntity = arrow;
        } else {
            System.out.println("[CannonShellDropper] Successfully created shell entity: " + shellEntity.getClass().getSimpleName());
        }

        return shellEntity;
    }

    /**
     * Attempts to create a shell entity from an item ID using various naming conventions
     */
    private Entity createShellEntityFromItem(String itemId, VehicleEntity vehicle, Vector4f position, Vector3f direction) {
        // First, try to get the projectile entity from Create Big Cannons munition system
        Entity shellEntity = createShellFromMunitionSystem(itemId, vehicle, position, direction);
        if (shellEntity != null) {
            return shellEntity;
        }

        // Special case mappings for known items with different entity names

        // For most Create Big Cannon shells, the entity ID matches the item ID
//        shellEntity = createShellEntity(itemId, vehicle, position, direction);
//        if (shellEntity != null) {
//            return shellEntity;
//        }

        // Try common projectile naming patterns
        String[] projectilePatterns = {
            itemId + "_projectile",
            itemId.replace("_shell", "_projectile"),
            itemId.replace("_shell", ""),
            itemId + "_entity"
        };

//        for (String pattern : projectilePatterns) {
//            shellEntity = createShellEntity(pattern, vehicle, position, direction);
//            if (shellEntity != null) {
//                return shellEntity;
//            }
//        }

        return null;
    }

    /**
     * Attempts to create a shell entity using Create Big Cannons munition system
     */
    private Entity createShellFromMunitionSystem(String itemId, VehicleEntity vehicle, Vector4f position, Vector3f direction) {
        try {
            // Get the item from the registry
            ResourceLocation itemLocation = ResourceLocation.tryParse(itemId);
            if (itemLocation == null) return null;

            if (!BuiltInRegistries.ITEM.containsKey(itemLocation)) return null;

            ItemStack shellStack = new ItemStack(BuiltInRegistries.ITEM.get(itemLocation));

            // Use the proper munition system approach
            EntityType<?> projectileType = findProjectileForShellItem(shellStack);
            if (projectileType != null) {
                Entity shell = projectileType.create(vehicle.level());
                if (shell != null) {
                    shell.setPos(position.x(), position.y() - 2, position.z());

                    // Set velocity - inherit aircraft velocity exactly
                    Vec3 aircraftVelocity = vehicle.getSpeedVector();
                    shell.setDeltaMovement(aircraftVelocity);

                    // Set owner if it's a projectile
                    if (shell instanceof AbstractArrow arrow && vehicle.getControllingPassenger() != null) {
                        arrow.setOwner(vehicle.getControllingPassenger());
                    }

                    // Apply shell properties from the item
                    setupShellProperties(shell, shellStack);

                    return shell;
                }
            }

            return null;

        } catch (Exception e) {
            // Any error, fall back to pattern matching
            return null;
        }
    }

    /**
     * Finds the projectile entity type for a given shell item ID
     */
    private EntityType<?> findProjectileEntityType(String itemId) {
        System.out.println("[CannonShellDropper] Finding projectile entity type for: " + itemId);

        try {
            // Method 1: Direct reflection access to the PROJECTILES map
            Class<?> munitionHandlerClass = Class.forName("rbasamoyai.createbigcannons.munitions.config.MunitionPropertiesHandler");
            java.lang.reflect.Field projectilesField = munitionHandlerClass.getDeclaredField("PROJECTILES");
            projectilesField.setAccessible(true);

            @SuppressWarnings("unchecked")
            Map<EntityType<?>, Object> projectilesMap = (Map<EntityType<?>, Object>) projectilesField.get(null);

            System.out.println("[CannonShellDropper] Found " + projectilesMap.size() + " registered projectiles");

            // Look for projectile entities that might correspond to this shell
            for (EntityType<?> entityType : projectilesMap.keySet()) {
                String entityId = BuiltInRegistries.ENTITY_TYPE.getKey(entityType).toString();
                System.out.println("[CannonShellDropper] Checking projectile: " + entityId + " against shell: " + itemId);

                // Check if this entity type matches our shell item
                if (isMatchingProjectile(itemId, entityId)) {
                    System.out.println("[CannonShellDropper] Found matching projectile: " + entityId);
                    return entityType;
                }
            }

            System.out.println("[CannonShellDropper] No matching projectile found in munition system");

        } catch (Exception e) {
            System.out.println("[CannonShellDropper] Reflection failed: " + e.getMessage());
            e.printStackTrace();
        }

        // Method 2: Pattern-based matching as fallback
        System.out.println("[CannonShellDropper] Falling back to pattern matching");
        return findProjectileByPattern(itemId);
    }

    /**
     * Checks if a projectile entity matches a shell item
     */
    private boolean isMatchingProjectile(String itemId, String entityId) {
        // Direct match
        if (itemId.equals(entityId)) {
            return true;
        }

        // Common patterns:
        // createbigcannons:he_shell -> createbigcannons:he_shell (direct)
        // createbigcannons:he_shell -> createbigcannons:he_shell_projectile
        // canonnukes:nuke_shell -> canonnukes:nuke_shell_projectile

        String[] patterns = {
            itemId + "_projectile",
            itemId.replace("_shell", "_projectile"),
            itemId.replace("_shell", "")
        };

        for (String pattern : patterns) {
            if (pattern.equals(entityId)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Finds projectile entity type using pattern matching
     */
    private EntityType<?> findProjectileByPattern(String itemId) {
        String[] patterns = {
            itemId,
            itemId + "_projectile",
            itemId.replace("_shell", "_projectile"),
            itemId.replace("_shell", "")
        };

        System.out.println("[CannonShellDropper] Trying pattern matching for: " + itemId);

        for (String pattern : patterns) {
            System.out.println("[CannonShellDropper] Trying pattern: " + pattern);
            ResourceLocation entityLocation = ResourceLocation.tryParse(pattern);
            if (entityLocation != null && BuiltInRegistries.ENTITY_TYPE.containsKey(entityLocation)) {
                System.out.println("[CannonShellDropper] Found entity by pattern: " + pattern);
                return BuiltInRegistries.ENTITY_TYPE.get(entityLocation);
            }
        }

        System.out.println("[CannonShellDropper] No entity found by pattern matching");
        return null;
    }

    private Entity createShellEntity(String entityId, VehicleEntity vehicle, Vector4f position, Vector3f direction) {
        try {
            ResourceLocation entityLocation = ResourceLocation.tryParse(entityId);
            if (entityLocation != null && BuiltInRegistries.ENTITY_TYPE.containsKey(entityLocation)) {
                EntityType<?> entityType = BuiltInRegistries.ENTITY_TYPE.get(entityLocation);
                Entity shell = entityType.create(vehicle.level());
//                System.out.println(shell.getUUID());
                if (shell != null) {
                    shell.setPos(position.x(), position.y()-2, position.z());

                    // Set velocity - inherit aircraft velocity exactly
                    Vec3 aircraftVelocity = vehicle.getSpeedVector();
                    shell.setDeltaMovement(aircraftVelocity);
                    // Set owner if it's a projectile
                    if (shell instanceof AbstractArrow arrow && vehicle.getControllingPassenger() != null) {
                        arrow.setOwner(vehicle.getControllingPassenger());
                    }

                    // Set up shell properties using NBT data
                    setupShellProperties(shell, ammoStack);

                    return shell;
                }
            }
        } catch (Exception e) {
            // If Create Big Cannons entity creation fails, return null to use fallback
        }

        return null;
    }

    private void setupShellProperties(Entity shell, ItemStack ammoStack) {

        try {
            CompoundTag shellNBT = new CompoundTag();
            shell.saveWithoutId(shellNBT);
            CompoundTag itemNBT = ammoStack.getTag();

            // Copy fuse data from the item to the shell entity

            if (itemNBT != null) {
                if (itemNBT.contains("BlockEntityTag") && itemNBT.get("BlockEntityTag") instanceof CompoundTag) {
                    if (((CompoundTag) itemNBT.get("BlockEntityTag")).contains("Fuze")) {

                        shellNBT.put("Fuze", ((CompoundTag) itemNBT.get("BlockEntityTag")).get("Fuze"));
                    }
                }
                shellNBT.putBoolean("HasBeenShot", true);

            }
            shell.load(shellNBT);
        } catch (Exception e) {
            // If NBT setup fails, shell will still work with default properties
        }
    }



    private Vector4f getDropOffset() {
        return new Vector4f(0.0f, -1.0f, 0.0f, 1.0f);
    }

    private Vector3f getDirection() {
        Vector3f direction = new Vector3f(0, -1.0f, 0);
        direction.mul(new Matrix3f(getMount().transform()));
        direction.mul(getEntity().getVehicleNormalTransform());
        return direction;
    }

    /**
     * Auto-detects if an item is a Create Big Cannon shell or compatible ammunition
     */
    private boolean isCannonShell(ItemStack stack) {
        if (stack.isEmpty()) {
            return false;
        }

        String itemId = BuiltInRegistries.ITEM.getKey(stack.getItem()).toString();
        System.out.println("[CannonShellDropper] Checking if item is cannon shell: " + itemId);

        // Check fallback items first
        if (FALLBACK_AMMUNITION.contains(itemId)) {
            System.out.println("[CannonShellDropper] Item is fallback ammunition: " + itemId);
            return true;
        }

        // Check if it's registered in Create Big Cannons munition system
        if (isRegisteredInMunitionSystem(stack)) {
            System.out.println("[CannonShellDropper] Item is registered in munition system: " + itemId);
            return true;
        }

        // Check if it has Create Big Cannon shell NBT structure
        if (hasCannonShellNBT(stack)) {
            System.out.println("[CannonShellDropper] Item has cannon shell NBT: " + itemId);
            return true;
        }

        // Skip name-based detection - focus on munition system and NBT only

        System.out.println("[CannonShellDropper] Item is not a cannon shell: " + itemId);
        return false;
    }

    /**
     * Checks if the item is registered in Create Big Cannons munition system
     * This is the proper way - check if the item can be used to create a registered projectile
     */
    private boolean isRegisteredInMunitionSystem(ItemStack stack) {
        try {
            // Try to access Create Big Cannons classes
            Class<?> munitionHandlerClass = Class.forName("rbasamoyai.createbigcannons.munitions.config.MunitionPropertiesHandler");

            String itemId = BuiltInRegistries.ITEM.getKey(stack.getItem()).toString();
            System.out.println("[CannonShellDropper] Checking munition system for: " + itemId);

            // Debug: Print all registered projectiles (only once)
            debugPrintRegisteredProjectiles();

            // The key insight: instead of guessing projectile names,
            // let's check if this item can actually create a shell entity
            EntityType<?> projectileType = findProjectileForShellItem(stack);
            if (projectileType != null) {
                System.out.println("[CannonShellDropper] Found projectile type: " + BuiltInRegistries.ENTITY_TYPE.getKey(projectileType));
                return true;
            } else {
                System.out.println("[CannonShellDropper] No projectile type found for: " + itemId);
            }

            return false;

        } catch (ClassNotFoundException e) {
            System.out.println("[CannonShellDropper] Create Big Cannons not available: " + e.getMessage());
            return false;
        } catch (Exception e) {
            System.out.println("[CannonShellDropper] Error checking munition system: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Finds the projectile entity type that corresponds to a shell item
     * This uses the actual Create Big Cannons logic instead of name guessing
     */
    private EntityType<?> findProjectileForShellItem(ItemStack shellStack) {
        try {
            // Method 1: Try to use Create Big Cannons' own logic to determine projectile type
            // This might involve checking if the item implements certain interfaces or has certain properties

            // Method 2: Check all registered projectiles and see if any can be created from this item
            Class<?> munitionHandlerClass = Class.forName("rbasamoyai.createbigcannons.munitions.config.MunitionPropertiesHandler");
            java.lang.reflect.Field projectilesField = munitionHandlerClass.getDeclaredField("PROJECTILES");
            projectilesField.setAccessible(true);

            @SuppressWarnings("unchecked")
            Map<EntityType<?>, Object> projectilesMap = (Map<EntityType<?>, Object>) projectilesField.get(null);

            // Try to find a projectile that can be created from this shell item
            // This is where we'd need to understand how Create Big Cannons maps items to projectiles
            String itemId = BuiltInRegistries.ITEM.getKey(shellStack.getItem()).toString();

            for (EntityType<?> projectileType : projectilesMap.keySet()) {
                String projectileId = BuiltInRegistries.ENTITY_TYPE.getKey(projectileType).toString();

                // Try some common patterns but also check if the projectile can actually be created
                if (isProjectileForItem(itemId, projectileId, shellStack, projectileType)) {
                    return projectileType;
                }
            }

            return null;

        } catch (Exception e) {
            System.out.println("[CannonShellDropper] Error finding projectile for shell: " + e.getMessage());
            return null;
        }
    }

    /**
     * Checks if a projectile entity type can be created from a shell item
     */
    private boolean isProjectileForItem(String itemId, String projectileId, ItemStack shellStack, EntityType<?> projectileType) {
        // Method 1: Try to actually create the projectile and see if it works
        try {
            // This is the most reliable way - try to create the entity and see if it accepts the shell data
            Entity testProjectile = projectileType.create(getEntity().level());
            if (testProjectile != null) {
                // Try to apply shell properties to see if it's compatible
                // If this doesn't throw an exception, it's likely compatible
                setupShellProperties(testProjectile, shellStack);
                testProjectile.discard(); // Clean up test entity
                return true;
            }
        } catch (Exception e) {
            // If creation fails, this projectile type doesn't work with this shell
        }

        return false;
    }

    /**
     * Debug method to print all registered projectiles
     */
    private void debugPrintRegisteredProjectiles() {
        try {
            Class<?> munitionHandlerClass = Class.forName("rbasamoyai.createbigcannons.munitions.config.MunitionPropertiesHandler");
            java.lang.reflect.Field projectilesField = munitionHandlerClass.getDeclaredField("PROJECTILES");
            projectilesField.setAccessible(true);

            @SuppressWarnings("unchecked")
            Map<EntityType<?>, Object> projectilesMap = (Map<EntityType<?>, Object>) projectilesField.get(null);

            System.out.println("[CannonShellDropper] Registered projectiles in munition system:");
            for (EntityType<?> entityType : projectilesMap.keySet()) {
                String entityId = BuiltInRegistries.ENTITY_TYPE.getKey(entityType).toString();
                System.out.println("  - " + entityId);
            }

        } catch (Exception e) {
            System.out.println("[CannonShellDropper] Failed to debug projectiles: " + e.getMessage());
        }
    }

    /**
     * Checks if a projectile entity type is registered in the Create Big Cannons munition system
     */
    private boolean isProjectileRegistered(EntityType<?> projectileType) {
        try {
            Class<?> munitionHandlerClass = Class.forName("rbasamoyai.createbigcannons.munitions.config.MunitionPropertiesHandler");
            java.lang.reflect.Field projectilesField = munitionHandlerClass.getDeclaredField("PROJECTILES");
            projectilesField.setAccessible(true);

            @SuppressWarnings("unchecked")
            Map<EntityType<?>, Object> projectilesMap = (Map<EntityType<?>, Object>) projectilesField.get(null);

            return projectilesMap.containsKey(projectileType);

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Checks if the item has the characteristic NBT structure of a Create Big Cannon shell
     */
    private boolean hasCannonShellNBT(ItemStack stack) {
        CompoundTag itemNBT = stack.getTag();
        if (itemNBT != null && itemNBT.contains("BlockEntityTag")) {
            CompoundTag blockEntityTag = itemNBT.getCompound("BlockEntityTag");
            // Create Big Cannon shells typically have a Fuze tag in their BlockEntityTag
            return blockEntityTag.contains("Fuze");
        }
        return false;
    }



    protected boolean spentAmmo(int amount) {
        if (ammo < amount && getEntity() instanceof InventoryVehicleEntity vehicle) {
            for (int i = 0; i < vehicle.getInventory().getContainerSize(); i++) {
                ItemStack stack = vehicle.getInventory().getItem(i);
                if (isCannonShell(stack)) {
                    ammoStack = stack.copy();
                    ammoStack.setCount(1); // Only drop one shell at a time

                    if (!getEntity().isPilotCreative()) {
                        ammo += 1; // Each shell counts as 1 ammo
                        stack.shrink(1);
                    }
                    break;
                }
            }
        }

        if (getEntity().isPilotCreative()) {
            // In creative mode, create a default shell if no ammo stack exists
            if (ammoStack == null || ammoStack.isEmpty()) {
                // Create a default Create Big Cannon shell for creative mode
                return false;
//                ammoStack = createDefaultShell();
            }
            return ammoStack != null && !ammoStack.isEmpty();
        }

        if (ammo <= 0) {
            if (getEntity().getControllingPassenger() instanceof Player player) {
                player.displayClientMessage(Component.translatable("immersive_aircraft.out_of_ammo"), true);
            }
            return false;
        }

        ammo -= amount;
        return true;
    }

    /**
     * Creates a default shell for creative mode
     */
    private ItemStack createDefaultShell() {
        // Try to find any available Create Big Cannon shell
        ItemStack shell = findAnyAvailableShell();
        if (shell != null) {
            return shell;
        }

        // Fallback to TNT
        return new ItemStack(BuiltInRegistries.ITEM.get(ResourceLocation.tryParse("minecraft:tnt")));
    }

    /**
     * Finds any available Create Big Cannon shell for creative mode
     */
    private ItemStack findAnyAvailableShell() {
        // List of shell types to try in order of preference
        String[] preferredShells = {
            "createbigcannons:he_shell",
            "createbigcannons:ap_shell",
            "createbigcannons:shrapnel_shell",
            "createbigcannons:smoke_shell",
            "canonnukes:nuke_shell"
        };

        for (String shellId : preferredShells) {
            try {
                ResourceLocation shellLocation = ResourceLocation.tryParse(shellId);
                if (shellLocation != null && BuiltInRegistries.ITEM.containsKey(shellLocation)) {
                    return new ItemStack(BuiltInRegistries.ITEM.get(shellLocation));
                }
            } catch (Exception e) {
                // Continue to next shell type
            }
        }

        return null;
    }

    /**
     * Gets the projectile entity type for a given shell item using Create Big Cannons munition system
     */
    private Optional<EntityType<?>> getProjectileEntityType(ItemStack shellStack) {
        try {
            // This is where you would integrate with Create Big Cannons munition system
            // The MunitionPropertiesHandler contains mappings from items to projectile entities
            // For now, we'll use pattern-based detection as a fallback

            String itemId = BuiltInRegistries.ITEM.getKey(shellStack.getItem()).toString();

            // Try to find corresponding projectile entity
            String[] projectilePatterns = {
                itemId,  // Direct match
                itemId + "_projectile",
                itemId.replace("_shell", "_projectile"),
                itemId.replace("_shell", "")
            };

            for (String pattern : projectilePatterns) {
                ResourceLocation entityLocation = ResourceLocation.tryParse(pattern);
                if (entityLocation != null && BuiltInRegistries.ENTITY_TYPE.containsKey(entityLocation)) {
                    return Optional.of(BuiltInRegistries.ENTITY_TYPE.get(entityLocation));
                }
            }

            return Optional.empty();

        } catch (Exception e) {
            return Optional.empty();
        }
    }
}
