package immersive_aircraft.entity.weapon;

import immersive_aircraft.entity.VehicleEntity;
import immersive_aircraft.entity.InventoryVehicleEntity;
import immersive_aircraft.entity.misc.WeaponMount;
import immersive_aircraft.cobalt.network.NetworkHandler;
import immersive_aircraft.network.c2s.FireMessage;
import net.minecraft.core.registries.BuiltInRegistries;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;

import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraft.world.entity.projectile.Arrow;
import net.minecraft.world.item.ItemStack;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.phys.Vec3;
import org.joml.Matrix3f;
import org.joml.Vector3f;
import org.joml.Vector4f;

import java.util.Map;
import java.util.Set;

public class CannonShellDropper extends Weapon {
    private static final float MAX_COOLDOWN = 2.0f;
    private float cooldown = 0.0f;
    private int ammo = 0;
    private ItemStack ammoStack;

    // Fallback items if Create Big Cannons isn't available
    private static final Set<String> FALLBACK_AMMUNITION = Set.of(
            "minecraft:tnt",
            "minecraft:fire_charge"
    );

    public CannonShellDropper(VehicleEntity entity, ItemStack stack, WeaponMount mount, int slot) {
        super(entity, stack, mount, slot);
    }

    @Override
    public void tick() {
        cooldown -= 1.0f / 20.0f;
    }

    @Override
    public void fire(Vector3f direction) {
        if (spentAmmo(1)) {
            dropCannonShell(direction);
        }
    }

    @Override
    public void clientFire(int index) {
        if (cooldown <= 0.0f) {
            cooldown = MAX_COOLDOWN;
            NetworkHandler.sendToServer(new FireMessage(getSlot(), index, getDirection()));
        }

    }
    private void dropCannonShell(Vector3f direction) {
        // Calculate the drop position
        Vector4f position = getDropOffset();
        VehicleEntity entity = getEntity();
        position.mul(getMount().transform());
        position.mul(entity.getVehicleTransform());

        // Try to spawn Create Big Cannon shell entity
        if (ammoStack != null && !ammoStack.isEmpty()) {
            Entity shellEntity = createCannonShellEntity(entity, ammoStack, position, direction);

            if (shellEntity != null) {
                entity.level().addFreshEntity(shellEntity);
            }
        }

        // Play sound
        getEntity().playSound(SoundEvents.DISPENSER_DISPENSE, 1.0f, 0.8f + 0.4f * entity.level().random.nextFloat());
    }





    private Entity createCannonShellEntity(VehicleEntity vehicle, ItemStack ammoStack, Vector4f position, Vector3f direction) {
        String itemId = BuiltInRegistries.ITEM.getKey(ammoStack.getItem()).toString();

        // Try to create shell entity based on auto-detection
        Entity shellEntity = null;

        // First, try to create the corresponding projectile entity
        shellEntity = createShellEntityFromItem(itemId, vehicle, position, direction);

        if (shellEntity == null) {
            // Fallback: create a simple arrow projectile for vanilla items
            Arrow arrow = new Arrow(vehicle.level(), position.x(), position.y(), position.z());
            arrow.setOwner(vehicle.getControllingPassenger());
            // Inherit aircraft velocity exactly
            arrow.setDeltaMovement(vehicle.getDeltaMovement());
            shellEntity = arrow;
        }

        return shellEntity;
    }

    /**
     * Attempts to create a shell entity from an item ID using various naming conventions
     */
    private Entity createShellEntityFromItem(String itemId, VehicleEntity vehicle, Vector4f position, Vector3f direction) {
        // Special case mappings for known items with different entity names
        if ("canonnukes:nuke_shell".equals(itemId)) {
            return createShellEntity("canonnukes:nuke_shell_projectile", vehicle, position, direction);
        }

        // For most Create Big Cannon shells, the entity ID matches the item ID
        Entity shellEntity = createShellEntity(itemId, vehicle, position, direction);
        if (shellEntity != null) {
            return shellEntity;
        }

        // Try common projectile naming patterns
        String[] projectilePatterns = {
            itemId + "_projectile",
            itemId.replace("_shell", "_projectile"),
            itemId.replace("_shell", ""),
            itemId + "_entity"
        };

        for (String pattern : projectilePatterns) {
            shellEntity = createShellEntity(pattern, vehicle, position, direction);
            if (shellEntity != null) {
                return shellEntity;
            }
        }

        return null;
    }

    private Entity createShellEntity(String entityId, VehicleEntity vehicle, Vector4f position, Vector3f direction) {
        try {
            ResourceLocation entityLocation = ResourceLocation.tryParse(entityId);
            if (entityLocation != null && BuiltInRegistries.ENTITY_TYPE.containsKey(entityLocation)) {
                EntityType<?> entityType = BuiltInRegistries.ENTITY_TYPE.get(entityLocation);
                Entity shell = entityType.create(vehicle.level());
//                System.out.println(shell.getUUID());
                if (shell != null) {
                    shell.setPos(position.x(), position.y()-2, position.z());

                    // Set velocity - inherit aircraft velocity exactly
                    Vec3 aircraftVelocity = vehicle.getSpeedVector();
                    shell.setDeltaMovement(aircraftVelocity);
                    // Set owner if it's a projectile
                    if (shell instanceof AbstractArrow arrow && vehicle.getControllingPassenger() != null) {
                        arrow.setOwner(vehicle.getControllingPassenger());
                    }

                    // Set up shell properties using NBT data
                    setupShellProperties(shell, ammoStack);

                    return shell;
                }
            }
        } catch (Exception e) {
            // If Create Big Cannons entity creation fails, return null to use fallback
        }

        return null;
    }

    private void setupShellProperties(Entity shell, ItemStack ammoStack) {

        try {
            CompoundTag shellNBT = new CompoundTag();
            shell.saveWithoutId(shellNBT);
            CompoundTag itemNBT = ammoStack.getTag();

            // Copy fuse data from the item to the shell entity

            if (itemNBT != null) {
                if (itemNBT.contains("BlockEntityTag") && itemNBT.get("BlockEntityTag") instanceof CompoundTag) {
                    if (((CompoundTag) itemNBT.get("BlockEntityTag")).contains("Fuze")) {

                        shellNBT.put("Fuze", ((CompoundTag) itemNBT.get("BlockEntityTag")).get("Fuze"));
                    }
                }
                shellNBT.putBoolean("HasBeenShot", true);

            }
            shell.load(shellNBT);
        } catch (Exception e) {
            // If NBT setup fails, shell will still work with default properties
        }
    }



    private Vector4f getDropOffset() {
        return new Vector4f(0.0f, -1.0f, 0.0f, 1.0f);
    }

    private Vector3f getDirection() {
        Vector3f direction = new Vector3f(0, -1.0f, 0);
        direction.mul(new Matrix3f(getMount().transform()));
        direction.mul(getEntity().getVehicleNormalTransform());
        return direction;
    }

    /**
     * Auto-detects if an item is a Create Big Cannon shell or compatible ammunition
     */
    private boolean isCannonShell(ItemStack stack) {
        if (stack.isEmpty()) {
            return false;
        }

        String itemId = BuiltInRegistries.ITEM.getKey(stack.getItem()).toString();

        // Check fallback items first
        if (FALLBACK_AMMUNITION.contains(itemId)) {
            return true;
        }

        // Check if it has Create Big Cannon shell NBT structure
        if (hasCannonShellNBT(stack)) {
            return true;
        }

        // Check if it follows Create Big Cannon naming patterns
        if (stack.getItem() instanceof ) {
            return true;
        }

        return false;
    }

    /**
     * Checks if the item has the characteristic NBT structure of a Create Big Cannon shell
     */
    private boolean hasCannonShellNBT(ItemStack stack) {
        CompoundTag itemNBT = stack.getTag();
        if (itemNBT != null && itemNBT.contains("BlockEntityTag")) {
            CompoundTag blockEntityTag = itemNBT.getCompound("BlockEntityTag");
            // Create Big Cannon shells typically have a Fuze tag in their BlockEntityTag
            return blockEntityTag.contains("Fuze");
        }
        return false;
    }

    /**
     * Checks if the item name follows Create Big Cannon shell naming patterns
     */


    protected boolean spentAmmo(int amount) {
        if (ammo < amount && getEntity() instanceof InventoryVehicleEntity vehicle) {
            for (int i = 0; i < vehicle.getInventory().getContainerSize(); i++) {
                ItemStack stack = vehicle.getInventory().getItem(i);
                if (isCannonShell(stack)) {
                    ammoStack = stack.copy();
                    ammoStack.setCount(1); // Only drop one shell at a time

                    if (!getEntity().isPilotCreative()) {
                        ammo += 1; // Each shell counts as 1 ammo
                        stack.shrink(1);
                    }
                    break;
                }
            }
        }

        if (getEntity().isPilotCreative()) {
            // In creative mode, create a default shell if no ammo stack exists
            if (ammoStack == null || ammoStack.isEmpty()) {
                // Create a default Create Big Cannon shell for creative mode
                ammoStack = createDefaultShell();
            }
            return ammoStack != null && !ammoStack.isEmpty();
        }

        if (ammo <= 0) {
            if (getEntity().getControllingPassenger() instanceof Player player) {
                player.displayClientMessage(Component.translatable("immersive_aircraft.out_of_ammo"), true);
            }
            return false;
        }

        ammo -= amount;
        return true;
    }

    /**
     * Creates a default shell for creative mode
     */
    private ItemStack createDefaultShell() {
        // Try to create a Create Big Cannon HE shell if available
        try {
            ResourceLocation heShellId = ResourceLocation.tryParse("createbigcannons:he_shell");
            if (heShellId != null && BuiltInRegistries.ITEM.containsKey(heShellId)) {
                return new ItemStack(BuiltInRegistries.ITEM.get(heShellId));
            }
        } catch (Exception e) {
            // Fall through to fallback
        }

        // Fallback to TNT
        return new ItemStack(BuiltInRegistries.ITEM.get(ResourceLocation.tryParse("minecraft:tnt")));
    }
}
