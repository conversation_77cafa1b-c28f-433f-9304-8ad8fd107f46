package immersive_aircraft.entity.weapon;

import immersive_aircraft.entity.VehicleEntity;
import immersive_aircraft.entity.InventoryVehicleEntity;
import immersive_aircraft.entity.misc.WeaponMount;
import immersive_aircraft.cobalt.network.NetworkHandler;
import immersive_aircraft.network.c2s.FireMessage;
import net.minecraft.core.registries.BuiltInRegistries;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;

import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraft.world.entity.projectile.Arrow;
import net.minecraft.world.item.ItemStack;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.phys.Vec3;
import org.joml.Matrix3f;
import org.joml.Vector3f;
import org.joml.Vector4f;
import rbasamoyai.createbigcannons.cannons.big_cannons.BigCannonBlockItem;
import rbasamoyai.createbigcannons.munitions.FuzedProjectileBlockItem;
import rbasamoyai.createbigcannons.munitions.big_cannon.ProjectileBlock;

import java.util.Map;
import java.util.Set;
import java.util.Optional;

public class CannonShellDropper extends Weapon {
    private static final float MAX_COOLDOWN = 2.0f;
    private float cooldown = 0.0f;
    private int ammo = 0;
    private ItemStack ammoStack;

    // Fallback items if Create Big Cannons isn't available
    private static final Set<String> FALLBACK_AMMUNITION = Set.of(
            "minecraft:tnt",
            "minecraft:fire_charge"
    );

    public CannonShellDropper(VehicleEntity entity, ItemStack stack, WeaponMount mount, int slot) {
        super(entity, stack, mount, slot);
    }

    @Override
    public void tick() {
        cooldown -= 1.0f / 20.0f;
    }

    @Override
    public void fire(Vector3f direction) {
        if (spentAmmo(1)) {
            dropCannonShell(direction);
        }
    }

    @Override
    public void clientFire(int index) {
        if (cooldown <= 0.0f) {
            cooldown = MAX_COOLDOWN;
            NetworkHandler.sendToServer(new FireMessage(getSlot(), index, getDirection()));
        }

    }
    private void dropCannonShell(Vector3f direction) {
        // Calculate the drop position
        Vector4f position = getDropOffset();
        VehicleEntity entity = getEntity();
        position.mul(getMount().transform());
        position.mul(entity.getVehicleTransform());

        // Try to spawn Create Big Cannon shell entity
        if (ammoStack != null && !ammoStack.isEmpty()) {
            Entity shellEntity = createCannonShellEntity(entity, ammoStack, position, direction);

            if (shellEntity != null) {
                entity.level().addFreshEntity(shellEntity);
            }
        }

        // Play sound
        getEntity().playSound(SoundEvents.DISPENSER_DISPENSE, 1.0f, 0.8f + 0.4f * entity.level().random.nextFloat());
    }





    private Entity createCannonShellEntity(VehicleEntity vehicle, ItemStack ammoStack, Vector4f position, Vector3f direction) {
        String itemId = BuiltInRegistries.ITEM.getKey(ammoStack.getItem()).toString();

        // Try to create shell entity based on auto-detection
        Entity shellEntity = null;

        // First, try to create the corresponding projectile entity
        shellEntity = createShellEntityFromItem(itemId, vehicle, position, direction);

        if (shellEntity == null) {
            // Fallback: create a simple arrow projectile for vanilla items
            Arrow arrow = new Arrow(vehicle.level(), position.x(), position.y(), position.z());
            arrow.setOwner(vehicle.getControllingPassenger());
            // Inherit aircraft velocity exactly
            arrow.setDeltaMovement(vehicle.getDeltaMovement());
            shellEntity = arrow;
        }

        return shellEntity;
    }

    /**
     * Attempts to create a shell entity from an item ID using various naming conventions
     */
    private Entity createShellEntityFromItem(String itemId, VehicleEntity vehicle, Vector4f position, Vector3f direction) {
        // First, try to get the projectile entity from Create Big Cannons munition system
        Entity shellEntity = createShellFromMunitionSystem(itemId, vehicle, position, direction);
        if (shellEntity != null) {
            return shellEntity;
        }

        // Special case mappings for known items with different entity names

        // For most Create Big Cannon shells, the entity ID matches the item ID
//        shellEntity = createShellEntity(itemId, vehicle, position, direction);
//        if (shellEntity != null) {
//            return shellEntity;
//        }

        // Try common projectile naming patterns
        String[] projectilePatterns = {
            itemId + "_projectile",
            itemId.replace("_shell", "_projectile"),
            itemId.replace("_shell", ""),
            itemId + "_entity"
        };

//        for (String pattern : projectilePatterns) {
//            shellEntity = createShellEntity(pattern, vehicle, position, direction);
//            if (shellEntity != null) {
//                return shellEntity;
//            }
//        }

        return null;
    }

    /**
     * Attempts to create a shell entity using Create Big Cannons munition system
     */
    private Entity createShellFromMunitionSystem(String itemId, VehicleEntity vehicle, Vector4f position, Vector3f direction) {
        try {
            // Try to access Create Big Cannons munition properties handler
            Class<?> munitionHandlerClass = Class.forName("rbasamoyai.createbigcannons.munitions.config.MunitionPropertiesHandler");

            // Get the item from the registry
            ResourceLocation itemLocation = ResourceLocation.tryParse(itemId);
            if (itemLocation == null) return null;

            if (!BuiltInRegistries.ITEM.containsKey(itemLocation)) return null;

            // Try to find a corresponding projectile entity type
            EntityType<?> projectileType = findProjectileEntityType(itemId);
            if (projectileType != null) {
                Entity shell = projectileType.create(vehicle.level());
                if (shell != null) {
                    shell.setPos(position.x(), position.y() - 2, position.z());

                    // Set velocity - inherit aircraft velocity exactly
                    Vec3 aircraftVelocity = vehicle.getSpeedVector();
                    shell.setDeltaMovement(aircraftVelocity);

                    // Set owner if it's a projectile
                    if (shell instanceof AbstractArrow arrow && vehicle.getControllingPassenger() != null) {
                        arrow.setOwner(vehicle.getControllingPassenger());
                    }

                    return shell;
                }
            }

            return null;

        } catch (ClassNotFoundException e) {
            // Create Big Cannons not available, fall back to pattern matching
            return null;
        } catch (Exception e) {
            // Any other error, fall back to pattern matching
            return null;
        }
    }

    /**
     * Finds the projectile entity type for a given shell item ID
     */
    private EntityType<?> findProjectileEntityType(String itemId) {
        try {
            // Method 1: Direct reflection access to the PROJECTILES map
            Class<?> munitionHandlerClass = Class.forName("rbasamoyai.createbigcannons.munitions.config.MunitionPropertiesHandler");
            java.lang.reflect.Field projectilesField = munitionHandlerClass.getDeclaredField("PROJECTILES");
            projectilesField.setAccessible(true);

            @SuppressWarnings("unchecked")
            Map<EntityType<?>, Object> projectilesMap = (Map<EntityType<?>, Object>) projectilesField.get(null);

            // Look for projectile entities that might correspond to this shell
            for (EntityType<?> entityType : projectilesMap.keySet()) {
                String entityId = BuiltInRegistries.ENTITY_TYPE.getKey(entityType).toString();

                // Check if this entity type matches our shell item
                if (isMatchingProjectile(itemId, entityId)) {
                    return entityType;
                }
            }

        } catch (Exception e) {
            // Reflection failed, fall back to pattern matching
        }

        // Method 2: Pattern-based matching as fallback
        return findProjectileByPattern(itemId);
    }

    /**
     * Checks if a projectile entity matches a shell item
     */
    private boolean isMatchingProjectile(String itemId, String entityId) {
        // Direct match
        if (itemId.equals(entityId)) {
            return true;
        }

        // Common patterns:
        // createbigcannons:he_shell -> createbigcannons:he_shell (direct)
        // createbigcannons:he_shell -> createbigcannons:he_shell_projectile
        // canonnukes:nuke_shell -> canonnukes:nuke_shell_projectile

        String[] patterns = {
            itemId + "_projectile",
            itemId.replace("_shell", "_projectile"),
            itemId.replace("_shell", "")
        };

        for (String pattern : patterns) {
            if (pattern.equals(entityId)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Finds projectile entity type using pattern matching
     */
    private EntityType<?> findProjectileByPattern(String itemId) {
        String[] patterns = {
            itemId,
            itemId + "_projectile",
            itemId.replace("_shell", "_projectile"),
            itemId.replace("_shell", "")
        };

        for (String pattern : patterns) {
            ResourceLocation entityLocation = ResourceLocation.tryParse(pattern);
            if (entityLocation != null && BuiltInRegistries.ENTITY_TYPE.containsKey(entityLocation)) {
                return BuiltInRegistries.ENTITY_TYPE.get(entityLocation);
            }
        }

        return null;
    }

    private Entity createShellEntity(String entityId, VehicleEntity vehicle, Vector4f position, Vector3f direction) {
        try {
            ResourceLocation entityLocation = ResourceLocation.tryParse(entityId);
            if (entityLocation != null && BuiltInRegistries.ENTITY_TYPE.containsKey(entityLocation)) {
                EntityType<?> entityType = BuiltInRegistries.ENTITY_TYPE.get(entityLocation);
                Entity shell = entityType.create(vehicle.level());
//                System.out.println(shell.getUUID());
                if (shell != null) {
                    shell.setPos(position.x(), position.y()-2, position.z());

                    // Set velocity - inherit aircraft velocity exactly
                    Vec3 aircraftVelocity = vehicle.getSpeedVector();
                    shell.setDeltaMovement(aircraftVelocity);
                    // Set owner if it's a projectile
                    if (shell instanceof AbstractArrow arrow && vehicle.getControllingPassenger() != null) {
                        arrow.setOwner(vehicle.getControllingPassenger());
                    }

                    // Set up shell properties using NBT data
                    setupShellProperties(shell, ammoStack);

                    return shell;
                }
            }
        } catch (Exception e) {
            // If Create Big Cannons entity creation fails, return null to use fallback
        }

        return null;
    }

    private void setupShellProperties(Entity shell, ItemStack ammoStack) {

        try {
            CompoundTag shellNBT = new CompoundTag();
            shell.saveWithoutId(shellNBT);
            CompoundTag itemNBT = ammoStack.getTag();

            // Copy fuse data from the item to the shell entity

            if (itemNBT != null) {
                if (itemNBT.contains("BlockEntityTag") && itemNBT.get("BlockEntityTag") instanceof CompoundTag) {
                    if (((CompoundTag) itemNBT.get("BlockEntityTag")).contains("Fuze")) {

                        shellNBT.put("Fuze", ((CompoundTag) itemNBT.get("BlockEntityTag")).get("Fuze"));
                    }
                }
                shellNBT.putBoolean("HasBeenShot", true);

            }
            shell.load(shellNBT);
        } catch (Exception e) {
            // If NBT setup fails, shell will still work with default properties
        }
    }



    private Vector4f getDropOffset() {
        return new Vector4f(0.0f, -1.0f, 0.0f, 1.0f);
    }

    private Vector3f getDirection() {
        Vector3f direction = new Vector3f(0, -1.0f, 0);
        direction.mul(new Matrix3f(getMount().transform()));
        direction.mul(getEntity().getVehicleNormalTransform());
        return direction;
    }

    /**
     * Auto-detects if an item is a Create Big Cannon shell or compatible ammunition
     */
    private boolean isCannonShell(ItemStack stack) {
        if (stack.isEmpty()) {
            return false;
        }

        String itemId = BuiltInRegistries.ITEM.getKey(stack.getItem()).toString();

        // Check fallback items first
        if (FALLBACK_AMMUNITION.contains(itemId)) {
            return true;
        }

        // Check if it's registered in Create Big Cannons munition system
        if (isRegisteredInMunitionSystem(stack)) {
            return true;
        }

        // Check if it has Create Big Cannon shell NBT structure
        if (hasCannonShellNBT(stack)) {
            return true;
        }

        // Check if it follows Create Big Cannon naming patterns
        if (stack.getItem() instanceof FuzedProjectileBlockItem) {
            return true;
        }

        return false;
    }

    /**
     * Checks if the item is registered in Create Big Cannons munition system
     */
    private boolean isRegisteredInMunitionSystem(ItemStack stack) {
        try {
            // Try to access Create Big Cannons classes
            Class<?> munitionHandlerClass = Class.forName("rbasamoyai.createbigcannons.munitions.config.MunitionPropertiesHandler");

            String itemId = BuiltInRegistries.ITEM.getKey(stack.getItem()).toString();

            // Check if there's a corresponding projectile entity registered
            EntityType<?> projectileType = findProjectileEntityType(itemId);
            if (projectileType != null) {
                // Additional check: see if this projectile is actually registered in the munition system
                return isProjectileRegistered(projectileType);
            }

            return false;

        } catch (ClassNotFoundException e) {
            // Create Big Cannons not available
            return false;
        } catch (Exception e) {
            // Any other error
            return false;
        }
    }

    /**
     * Checks if a projectile entity type is registered in the Create Big Cannons munition system
     */
    private boolean isProjectileRegistered(EntityType<?> projectileType) {
        try {
            Class<?> munitionHandlerClass = Class.forName("rbasamoyai.createbigcannons.munitions.config.MunitionPropertiesHandler");
            java.lang.reflect.Field projectilesField = munitionHandlerClass.getDeclaredField("PROJECTILES");
            projectilesField.setAccessible(true);

            @SuppressWarnings("unchecked")
            Map<EntityType<?>, Object> projectilesMap = (Map<EntityType<?>, Object>) projectilesField.get(null);

            return projectilesMap.containsKey(projectileType);

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Checks if the item has the characteristic NBT structure of a Create Big Cannon shell
     */
    private boolean hasCannonShellNBT(ItemStack stack) {
        CompoundTag itemNBT = stack.getTag();
        if (itemNBT != null && itemNBT.contains("BlockEntityTag")) {
            CompoundTag blockEntityTag = itemNBT.getCompound("BlockEntityTag");
            // Create Big Cannon shells typically have a Fuze tag in their BlockEntityTag
            return blockEntityTag.contains("Fuze");
        }
        return false;
    }



    protected boolean spentAmmo(int amount) {
        if (ammo < amount && getEntity() instanceof InventoryVehicleEntity vehicle) {
            for (int i = 0; i < vehicle.getInventory().getContainerSize(); i++) {
                ItemStack stack = vehicle.getInventory().getItem(i);
                if (isCannonShell(stack)) {
                    ammoStack = stack.copy();
                    ammoStack.setCount(1); // Only drop one shell at a time

                    if (!getEntity().isPilotCreative()) {
                        ammo += 1; // Each shell counts as 1 ammo
                        stack.shrink(1);
                    }
                    break;
                }
            }
        }

        if (getEntity().isPilotCreative()) {
            // In creative mode, create a default shell if no ammo stack exists
            if (ammoStack == null || ammoStack.isEmpty()) {
                // Create a default Create Big Cannon shell for creative mode
                return false;
//                ammoStack = createDefaultShell();
            }
            return ammoStack != null && !ammoStack.isEmpty();
        }

        if (ammo <= 0) {
            if (getEntity().getControllingPassenger() instanceof Player player) {
                player.displayClientMessage(Component.translatable("immersive_aircraft.out_of_ammo"), true);
            }
            return false;
        }

        ammo -= amount;
        return true;
    }

    /**
     * Creates a default shell for creative mode
     */
    private ItemStack createDefaultShell() {
        // Try to find any available Create Big Cannon shell
        ItemStack shell = findAnyAvailableShell();
        if (shell != null) {
            return shell;
        }

        // Fallback to TNT
        return new ItemStack(BuiltInRegistries.ITEM.get(ResourceLocation.tryParse("minecraft:tnt")));
    }

    /**
     * Finds any available Create Big Cannon shell for creative mode
     */
    private ItemStack findAnyAvailableShell() {
        // List of shell types to try in order of preference
        String[] preferredShells = {
            "createbigcannons:he_shell",
            "createbigcannons:ap_shell",
            "createbigcannons:shrapnel_shell",
            "createbigcannons:smoke_shell",
            "canonnukes:nuke_shell"
        };

        for (String shellId : preferredShells) {
            try {
                ResourceLocation shellLocation = ResourceLocation.tryParse(shellId);
                if (shellLocation != null && BuiltInRegistries.ITEM.containsKey(shellLocation)) {
                    return new ItemStack(BuiltInRegistries.ITEM.get(shellLocation));
                }
            } catch (Exception e) {
                // Continue to next shell type
            }
        }

        return null;
    }

    /**
     * Gets the projectile entity type for a given shell item using Create Big Cannons munition system
     */
    private Optional<EntityType<?>> getProjectileEntityType(ItemStack shellStack) {
        try {
            // This is where you would integrate with Create Big Cannons munition system
            // The MunitionPropertiesHandler contains mappings from items to projectile entities
            // For now, we'll use pattern-based detection as a fallback

            String itemId = BuiltInRegistries.ITEM.getKey(shellStack.getItem()).toString();

            // Try to find corresponding projectile entity
            String[] projectilePatterns = {
                itemId,  // Direct match
                itemId + "_projectile",
                itemId.replace("_shell", "_projectile"),
                itemId.replace("_shell", "")
            };

            for (String pattern : projectilePatterns) {
                ResourceLocation entityLocation = ResourceLocation.tryParse(pattern);
                if (entityLocation != null && BuiltInRegistries.ENTITY_TYPE.containsKey(entityLocation)) {
                    return Optional.of(BuiltInRegistries.ENTITY_TYPE.get(entityLocation));
                }
            }

            return Optional.empty();

        } catch (Exception e) {
            return Optional.empty();
        }
    }
}
