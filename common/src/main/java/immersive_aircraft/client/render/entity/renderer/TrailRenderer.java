package immersive_aircraft.client.render.entity.renderer;

import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import immersive_aircraft.Main;
import immersive_aircraft.entity.misc.Trail;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.phys.Vec3;
import org.joml.Matrix3f;
import org.joml.Vector3f;

public class TrailRenderer {
    private static final ResourceLocation identifier = Main.locate("textures/entity/trail.png");

    public static void render(Trail trail, MultiBufferSource vertexConsumerProvider, PoseStack.Pose matrices) {
        render(trail, vertexConsumerProvider, matrices, null);
    }

    public static void render(Trail trail, MultiBufferSource vertexConsumerProvider, PoseStack.Pose matrices, float[] customColor) {
        if (trail.nullEntries >= trail.size || trail.entries == 0) {
            return;
        }

        // Use different render types for better visibility
        VertexConsumer lineVertexConsumer;
        if (customColor != null) {
            // For colored trails, use a more visible render type that doesn't get occluded
            lineVertexConsumer = vertexConsumerProvider.getBuffer(RenderType.beaconBeam(identifier, false)); // No depth test for better visibility
        } else {
            lineVertexConsumer = vertexConsumerProvider.getBuffer(RenderType.beaconBeam(identifier, true));
        }

        int light = 15728880; // Brighter light for better visibility

        Vec3 pos = Minecraft.getInstance().gameRenderer.getMainCamera().getPosition();
        Matrix3f matrix = matrices.normal();

        // For colored trails, render multiple layers to make them bigger and more voluminous
        if (customColor != null) {
            renderColoredTrail(trail, lineVertexConsumer, matrix, pos, light, customColor);

            // Render an additional bright core for maximum visibility
            renderTrailCore(trail, vertexConsumerProvider, matrix, pos, light, customColor);
        } else {
            renderDefaultTrail(trail, lineVertexConsumer, matrix, pos, light);
        }
    }

    private static void renderColoredTrail(Trail trail, VertexConsumer lineVertexConsumer, Matrix3f matrix, Vec3 pos, int light, float[] customColor) {
        // Render a single line that circles around the center line
        float[] offsets = {0.0f}; // Just one layer - the circling effect is handled in the vertex offset
        float[] alphaMultipliers = {1.0f}; // Full opacity for the single line

        for (int layer = 0; layer < offsets.length; layer++) {
            float offset = offsets[layer];
            float alphaMult = alphaMultipliers[layer];

            for (int i = 1; i < Math.min(trail.entries, trail.size); i++) {
                int pre = ((i + trail.lastIndex - 1) % trail.size) * 7;
                int index = ((i + trail.lastIndex) % trail.size) * 7;

                int a1 = (int) ((1.0f - ((float) i) / trail.size * 255) * trail.buffer[pre + 6] * alphaMult);
                int a2 = i == (trail.size - 1) ? 0 : (int) ((1.0f - ((float) i + 1) / trail.size * 255) * trail.buffer[index + 6] * alphaMult);

                vertexWithOffset(trail, lineVertexConsumer, matrix, 0, 0, pre, pos, a1, light, customColor, offset);
                vertexWithOffset(trail, lineVertexConsumer, matrix, 0, 1, pre + 3, pos, a1, light, customColor, offset);
                vertexWithOffset(trail, lineVertexConsumer, matrix, 1, 1, index + 3, pos, a2, light, customColor, offset);
                vertexWithOffset(trail, lineVertexConsumer, matrix, 1, 0, index, pos, a2, light, customColor, offset);

                vertexWithOffset(trail, lineVertexConsumer, matrix, 1, 0, index, pos, a2, light, customColor, offset);
                vertexWithOffset(trail, lineVertexConsumer, matrix, 1, 1, index + 3, pos, a2, light, customColor, offset);
                vertexWithOffset(trail, lineVertexConsumer, matrix, 0, 1, pre + 3, pos, a1, light, customColor, offset);
                vertexWithOffset(trail, lineVertexConsumer, matrix, 0, 0, pre, pos, a1, light, customColor, offset);
            }
        }
    }

    private static void renderDefaultTrail(Trail trail, VertexConsumer lineVertexConsumer, Matrix3f matrix, Vec3 pos, int light) {
        for (int i = 1; i < Math.min(trail.entries, trail.size); i++) {
            int pre = ((i + trail.lastIndex - 1) % trail.size) * 7;
            int index = ((i + trail.lastIndex) % trail.size) * 7;

            int a1 = (int) ((1.0f - ((float) i) / trail.size * 255) * trail.buffer[pre + 6]);
            int a2 = i == (trail.size - 1) ? 0 : (int) ((1.0f - ((float) i + 1) / trail.size * 255) * trail.buffer[index + 6]);

            vertex(trail, lineVertexConsumer, matrix, 0, 0, pre, pos, a1, light, null);
            vertex(trail, lineVertexConsumer, matrix, 0, 1, pre + 3, pos, a1, light, null);
            vertex(trail, lineVertexConsumer, matrix, 1, 1, index + 3, pos, a2, light, null);
            vertex(trail, lineVertexConsumer, matrix, 1, 0, index, pos, a2, light, null);

            vertex(trail, lineVertexConsumer, matrix, 1, 0, index, pos, a2, light, null);
            vertex(trail, lineVertexConsumer, matrix, 1, 1, index + 3, pos, a2, light, null);
            vertex(trail, lineVertexConsumer, matrix, 0, 1, pre + 3, pos, a1, light, null);
            vertex(trail, lineVertexConsumer, matrix, 0, 0, pre, pos, a1, light, null);
        }
    }

    private static void vertex(Trail trail, VertexConsumer lineVertexConsumer, Matrix3f matrix, float u, float v, int index, Vec3 pos, float a, int light, float[] customColor) {
        Vector3f p = new Vector3f((float) (trail.buffer[index] - pos.x), (float) (trail.buffer[index + 1] - pos.y), (float) (trail.buffer[index + 2] - pos.z));
        matrix.transform(p);

        // Use custom color if provided, otherwise use default gray
        if (customColor != null && customColor.length >= 3) {
            lineVertexConsumer.vertex(p.x, p.y, p.z, customColor[0], customColor[1], customColor[2], a, u, v, OverlayTexture.NO_OVERLAY, light, 1, 0, 0);
        } else {
            lineVertexConsumer.vertex(p.x, p.y, p.z, trail.gray, trail.gray, trail.gray, a, u, v, OverlayTexture.NO_OVERLAY, light, 1, 0, 0);
        }
    }

    private static void vertexWithOffset(Trail trail, VertexConsumer lineVertexConsumer, Matrix3f matrix, float u, float v, int index, Vec3 pos, float a, int light, float[] customColor, float offset) {
        Vector3f p = new Vector3f((float) (trail.buffer[index] - pos.x), (float) (trail.buffer[index + 1] - pos.y), (float) (trail.buffer[index + 2] - pos.z));

        // Add random offset to create volume and thickness - make it more pronounced
        p.add(
            (float) (Math.sin(index * 0.08) * offset * 1.5),
            (float) (Math.cos(index * 0.08) * offset * 1.5),
            (float) (Math.sin(index * 0.12) * offset * 1.5)
        );

        matrix.transform(p);

        // Enhance visibility with brighter colors and better lighting
        float brightnessBoost = 1.3f; // Make colors 30% brighter
        float enhancedR = Math.min(1.0f, customColor[0] * brightnessBoost);
        float enhancedG = Math.min(1.0f, customColor[1] * brightnessBoost);
        float enhancedB = Math.min(1.0f, customColor[2] * brightnessBoost);

        // Use maximum light value for better visibility
        int maxLight = 15728880; // Brighter than default

        lineVertexConsumer.vertex(p.x, p.y, p.z, enhancedR, enhancedG, enhancedB, a, u, v, OverlayTexture.NO_OVERLAY, maxLight, 1, 0, 0);
    }

    /**
     * Renders a bright core trail for maximum visibility
     */
    private static void renderTrailCore(Trail trail, MultiBufferSource vertexConsumerProvider, Matrix3f matrix, Vec3 pos, int light, float[] customColor) {
        // Use an even brighter render type for the core
        VertexConsumer coreVertexConsumer = vertexConsumerProvider.getBuffer(RenderType.lightning());

        for (int i = 1; i < Math.min(trail.entries, trail.size); i++) {
            int pre = ((i + trail.lastIndex - 1) % trail.size) * 7;
            int index = ((i + trail.lastIndex) % trail.size) * 7;

            int a1 = (int) ((1.0f - ((float) i) / trail.size * 255) * trail.buffer[pre + 6] * 1.2f); // Brighter alpha
            int a2 = i == (trail.size - 1) ? 0 : (int) ((1.0f - ((float) i + 1) / trail.size * 255) * trail.buffer[index + 6] * 1.2f);

            // Render a smaller, brighter core
            vertexCore(trail, coreVertexConsumer, matrix, 0, 0, pre, pos, a1, light, customColor);
            vertexCore(trail, coreVertexConsumer, matrix, 0, 1, pre + 3, pos, a1, light, customColor);
            vertexCore(trail, coreVertexConsumer, matrix, 1, 1, index + 3, pos, a2, light, customColor);
            vertexCore(trail, coreVertexConsumer, matrix, 1, 0, index, pos, a2, light, customColor);

            vertexCore(trail, coreVertexConsumer, matrix, 1, 0, index, pos, a2, light, customColor);
            vertexCore(trail, coreVertexConsumer, matrix, 1, 1, index + 3, pos, a2, light, customColor);
            vertexCore(trail, coreVertexConsumer, matrix, 0, 1, pre + 3, pos, a1, light, customColor);
            vertexCore(trail, coreVertexConsumer, matrix, 0, 0, pre, pos, a1, light, customColor);
        }
    }

    /**
     * Renders the bright core vertices
     */
    private static void vertexCore(Trail trail, VertexConsumer vertexConsumer, Matrix3f matrix, float u, float v, int index, Vec3 pos, float a, int light, float[] customColor) {
        Vector3f p = new Vector3f((float) (trail.buffer[index] - pos.x), (float) (trail.buffer[index + 1] - pos.y), (float) (trail.buffer[index + 2] - pos.z));
        matrix.transform(p);

        // Super bright colors for the core
        float coreBrightness = 1.8f;
        float coreR = Math.min(1.0f, customColor[0] * coreBrightness);
        float coreG = Math.min(1.0f, customColor[1] * coreBrightness);
        float coreB = Math.min(1.0f, customColor[2] * coreBrightness);

        // Maximum light and no overlay for best visibility
        vertexConsumer.vertex(p.x, p.y, p.z, coreR, coreG, coreB, a, u, v, OverlayTexture.NO_OVERLAY, 15728880, 1, 0, 0);
    }
}
