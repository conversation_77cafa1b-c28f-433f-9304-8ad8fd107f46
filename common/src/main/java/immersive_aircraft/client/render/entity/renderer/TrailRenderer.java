package immersive_aircraft.client.render.entity.renderer;

import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import immersive_aircraft.Main;
import immersive_aircraft.entity.misc.Trail;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.phys.Vec3;
import org.joml.Matrix3f;
import org.joml.Vector3f;

public class TrailRenderer {
    private static final ResourceLocation identifier = Main.locate("textures/entity/trail.png");

    public static void render(Trail trail, MultiBufferSource vertexConsumerProvider, PoseStack.Pose matrices) {
        render(trail, vertexConsumerProvider, matrices, null);
    }

    public static void render(Trail trail, MultiBufferSource vertexConsumerProvider, PoseStack.Pose matrices, float[] customColor) {
        if (trail.nullEntries >= trail.size || trail.entries == 0) {
            return;
        }

        VertexConsumer lineVertexConsumer = vertexConsumerProvider.getBuffer(RenderType.beaconBeam(identifier, true));
        int light = 15728640;

        Vec3 pos = Minecraft.getInstance().gameRenderer.getMainCamera().getPosition();
        Matrix3f matrix = matrices.normal();

        //todo a custom vertex indexing methode would be beneficial here
        for (int i = 1; i < Math.min(trail.entries, trail.size); i++) {
            int pre = ((i + trail.lastIndex - 1) % trail.size) * 7;
            int index = ((i + trail.lastIndex) % trail.size) * 7;

            int a1 = (int) ((1.0f - ((float) i) / trail.size * 255) * trail.buffer[pre + 6]);
            int a2 = i == (trail.size - 1) ? 0 : (int) ((1.0f - ((float) i + 1) / trail.size * 255) * trail.buffer[index + 6]);

            vertex(trail, lineVertexConsumer, matrix, 0, 0, pre, pos, a1, light, customColor);
            vertex(trail, lineVertexConsumer, matrix, 0, 1, pre + 3, pos, a1, light, customColor);
            vertex(trail, lineVertexConsumer, matrix, 1, 1, index + 3, pos, a2, light, customColor);
            vertex(trail, lineVertexConsumer, matrix, 1, 0, index, pos, a2, light, customColor);

            //todo the anti culling here is stupid
            vertex(trail, lineVertexConsumer, matrix, 1, 0, index, pos, a2, light, customColor);
            vertex(trail, lineVertexConsumer, matrix, 1, 1, index + 3, pos, a2, light, customColor);
            vertex(trail, lineVertexConsumer, matrix, 0, 1, pre + 3, pos, a1, light, customColor);
            vertex(trail, lineVertexConsumer, matrix, 0, 0, pre, pos, a1, light, customColor);
        }
    }

    private static void renderColoredTrail(Trail trail, VertexConsumer lineVertexConsumer, Matrix3f matrix, Vec3 pos, int light, float[] customColor) {
        // Render a single line that circles around the center line
        float[] offsets = {0.0f}; // Just one layer - the circling effect is handled in the vertex offset
        float[] alphaMultipliers = {1.0f}; // Full opacity for the single line

        for (int layer = 0; layer < offsets.length; layer++) {
            float offset = offsets[layer];
            float alphaMult = alphaMultipliers[layer];

            for (int i = 1; i < Math.min(trail.entries, trail.size); i++) {
                int pre = ((i + trail.lastIndex - 1) % trail.size) * 7;
                int index = ((i + trail.lastIndex) % trail.size) * 7;

                int a1 = (int) ((1.0f - ((float) i) / trail.size * 255) * trail.buffer[pre + 6] * alphaMult);
                int a2 = i == (trail.size - 1) ? 0 : (int) ((1.0f - ((float) i + 1) / trail.size * 255) * trail.buffer[index + 6] * alphaMult);

                vertexWithOffset(trail, lineVertexConsumer, matrix, 0, 0, pre, pos, a1, light, customColor, offset);
                vertexWithOffset(trail, lineVertexConsumer, matrix, 0, 1, pre + 3, pos, a1, light, customColor, offset);
                vertexWithOffset(trail, lineVertexConsumer, matrix, 1, 1, index + 3, pos, a2, light, customColor, offset);
                vertexWithOffset(trail, lineVertexConsumer, matrix, 1, 0, index, pos, a2, light, customColor, offset);

                vertexWithOffset(trail, lineVertexConsumer, matrix, 1, 0, index, pos, a2, light, customColor, offset);
                vertexWithOffset(trail, lineVertexConsumer, matrix, 1, 1, index + 3, pos, a2, light, customColor, offset);
                vertexWithOffset(trail, lineVertexConsumer, matrix, 0, 1, pre + 3, pos, a1, light, customColor, offset);
                vertexWithOffset(trail, lineVertexConsumer, matrix, 0, 0, pre, pos, a1, light, customColor, offset);
            }
        }
    }

    private static void renderDefaultTrail(Trail trail, VertexConsumer lineVertexConsumer, Matrix3f matrix, Vec3 pos, int light) {
        for (int i = 1; i < Math.min(trail.entries, trail.size); i++) {
            int pre = ((i + trail.lastIndex - 1) % trail.size) * 7;
            int index = ((i + trail.lastIndex) % trail.size) * 7;

            int a1 = (int) ((1.0f - ((float) i) / trail.size * 255) * trail.buffer[pre + 6]);
            int a2 = i == (trail.size - 1) ? 0 : (int) ((1.0f - ((float) i + 1) / trail.size * 255) * trail.buffer[index + 6]);

            vertex(trail, lineVertexConsumer, matrix, 0, 0, pre, pos, a1, light, null);
            vertex(trail, lineVertexConsumer, matrix, 0, 1, pre + 3, pos, a1, light, null);
            vertex(trail, lineVertexConsumer, matrix, 1, 1, index + 3, pos, a2, light, null);
            vertex(trail, lineVertexConsumer, matrix, 1, 0, index, pos, a2, light, null);

            vertex(trail, lineVertexConsumer, matrix, 1, 0, index, pos, a2, light, null);
            vertex(trail, lineVertexConsumer, matrix, 1, 1, index + 3, pos, a2, light, null);
            vertex(trail, lineVertexConsumer, matrix, 0, 1, pre + 3, pos, a1, light, null);
            vertex(trail, lineVertexConsumer, matrix, 0, 0, pre, pos, a1, light, null);
        }
    }

    private static void vertex(Trail trail, VertexConsumer lineVertexConsumer, Matrix3f matrix, float u, float v, int index, Vec3 pos, float a, int light, float[] customColor) {
        Vector3f p = new Vector3f((float) (trail.buffer[index] - pos.x), (float) (trail.buffer[index + 1] - pos.y), (float) (trail.buffer[index + 2] - pos.z));
        matrix.transform(p);

        // Use custom color if provided, otherwise use default gray
        if (customColor != null && customColor.length >= 3) {
            lineVertexConsumer.vertex(p.x, p.y, p.z, customColor[0], customColor[1], customColor[2], a, u, v, OverlayTexture.NO_OVERLAY, light, 1, 0, 0);
        } else {
            lineVertexConsumer.vertex(p.x, p.y, p.z, trail.gray, trail.gray, trail.gray, a, u, v, OverlayTexture.NO_OVERLAY, light, 1, 0, 0);
        }
    }

    private static void vertexWithOffset(Trail trail, VertexConsumer lineVertexConsumer, Matrix3f matrix, float u, float v, int index, Vec3 pos, float a, int light, float[] customColor, float offset) {
        Vector3f p = new Vector3f((float) (trail.buffer[index] - pos.x), (float) (trail.buffer[index + 1] - pos.y), (float) (trail.buffer[index + 2] - pos.z));

        // Create a circular motion around the center line for colored trails
        float circleRadius = 0.8f; // Radius of the circle around the center line
        float angle = index * 0.15f; // Angle progression along the trail

        // Add circular offset around the center line
        p.add(
            (float) (Math.sin(angle) * circleRadius),
            (float) (Math.cos(angle) * circleRadius),
            0.0f // No Z offset to keep it in the plane perpendicular to trail direction
        );

        matrix.transform(p);

        // Enhance visibility with slightly brighter colors and normal lighting
        float brightnessBoost = 1.1f; // Make colors 10% brighter (reduced from 30%)
        float enhancedR = Math.min(1.0f, customColor[0] * brightnessBoost);
        float enhancedG = Math.min(1.0f, customColor[1] * brightnessBoost);
        float enhancedB = Math.min(1.0f, customColor[2] * brightnessBoost);

        // Use normal light value for more natural appearance
        int maxLight = 15728640; // Standard light value (reduced from 15728880)

        lineVertexConsumer.vertex(p.x, p.y, p.z, enhancedR, enhancedG, enhancedB, a, u, v, OverlayTexture.NO_OVERLAY, maxLight, 1, 0, 0);
    }


}
